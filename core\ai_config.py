# core/ai_config.py
# AI服務配置管理模塊

import os
import json
from typing import Dict, Optional, List
from flask import current_app

class AIConfigManager:
    """AI服務配置管理器"""
    
    def __init__(self, config_file: str = None):
        """初始化配置管理器
        
        Args:
            config_file: 配置文件路徑，如果為None則使用默認路徑
        """
        if config_file is None:
            config_file = os.path.join(
                current_app.config.get('DATA_FOLDER', 'data'), 
                'ai_config.json'
            )
        self.config_file = config_file
        self._config = self._load_config()
    
    def _load_config(self) -> Dict:
        """載入配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            current_app.logger.warning(f"載入AI配置失敗: {e}")
        
        # 返回默認配置
        return {
            "current_service": "gemini",
            "services": {
                "gemini": {
                    "name": "Google Gemini",
                    "api_key": "",
                    "model": "gemini-pro",
                    "enabled": True,
                    "description": "Google的先進AI模型，支援多語言對話和創作"
                },
                "openai": {
                    "name": "OpenAI GPT",
                    "api_key": "",
                    "model": "gpt-3.5-turbo",
                    "enabled": False,
                    "description": "OpenAI的GPT模型，強大的文本生成能力"
                },
                "claude": {
                    "name": "Anthropic Claude",
                    "api_key": "",
                    "model": "claude-3-sonnet",
                    "enabled": False,
                    "description": "Anthropic的Claude模型，擅長長文本理解和創作"
                }
            },
            "generation_settings": {
                "temperature": 0.7,
                "max_tokens": 2048,
                "top_p": 0.9
            }
        }
    
    def _save_config(self) -> bool:
        """保存配置到文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            current_app.logger.error(f"保存AI配置失敗: {e}")
            return False
    
    def get_current_service(self) -> str:
        """獲取當前選擇的AI服務"""
        return self._config.get("current_service", "gemini")
    
    def set_current_service(self, service_name: str) -> bool:
        """設置當前AI服務"""
        if service_name in self._config["services"]:
            self._config["current_service"] = service_name
            return self._save_config()
        return False
    
    def get_service_config(self, service_name: str = None) -> Optional[Dict]:
        """獲取指定服務的配置"""
        if service_name is None:
            service_name = self.get_current_service()
        return self._config["services"].get(service_name)
    
    def set_api_key(self, service_name: str, api_key: str) -> bool:
        """設置指定服務的API key"""
        if service_name in self._config["services"]:
            self._config["services"][service_name]["api_key"] = api_key
            return self._save_config()
        return False
    
    def get_api_key(self, service_name: str = None) -> str:
        """獲取指定服務的API key"""
        if service_name is None:
            service_name = self.get_current_service()
        
        service_config = self.get_service_config(service_name)
        if service_config:
            # 優先使用配置文件中的API key，其次使用環境變量
            api_key = service_config.get("api_key", "")
            if not api_key:
                # 嘗試從環境變量獲取
                env_key_map = {
                    "gemini": "GOOGLE_API_KEY",
                    "openai": "OPENAI_API_KEY", 
                    "claude": "ANTHROPIC_API_KEY"
                }
                env_key = env_key_map.get(service_name)
                if env_key:
                    api_key = os.getenv(env_key, "")
            return api_key
        return ""
    
    def get_all_services(self) -> Dict:
        """獲取所有服務配置"""
        return self._config["services"]
    
    def enable_service(self, service_name: str, enabled: bool = True) -> bool:
        """啟用或禁用服務"""
        if service_name in self._config["services"]:
            self._config["services"][service_name]["enabled"] = enabled
            return self._save_config()
        return False
    
    def update_generation_settings(self, settings: Dict) -> bool:
        """更新生成設置"""
        self._config["generation_settings"].update(settings)
        return self._save_config()
    
    def get_generation_settings(self) -> Dict:
        """獲取生成設置"""
        return self._config["generation_settings"]
    
    def is_service_available(self, service_name: str = None) -> bool:
        """檢查服務是否可用（有API key且已啟用）"""
        if service_name is None:
            service_name = self.get_current_service()
        
        service_config = self.get_service_config(service_name)
        if not service_config or not service_config.get("enabled", False):
            return False
        
        api_key = self.get_api_key(service_name)
        return bool(api_key.strip())
    
    def get_available_services(self) -> List[Dict]:
        """獲取所有可用的服務列表"""
        available = []
        for service_name, config in self._config["services"].items():
            if config.get("enabled", False):
                available.append({
                    "name": service_name,
                    "display_name": config.get("name", service_name),
                    "description": config.get("description", ""),
                    "has_api_key": bool(self.get_api_key(service_name).strip()),
                    "is_current": service_name == self.get_current_service()
                })
        return available

# 全局配置管理器實例
_ai_config_manager = None

def get_ai_config_manager() -> AIConfigManager:
    """獲取全局AI配置管理器實例"""
    global _ai_config_manager
    if _ai_config_manager is None:
        _ai_config_manager = AIConfigManager()
    return _ai_config_manager
