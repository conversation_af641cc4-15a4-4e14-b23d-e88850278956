# core/ai_services.py
# 這個檔案負責與不同的 AI 服務進行互動，例如內容生成。

import os
import google.generativeai as genai
from flask import current_app
from .ai_config import get_ai_config_manager

# 支援的AI服務配置
AI_SERVICES = {
    'gemini': {
        'module': genai,
        'configure_func': lambda api_key: genai.configure(api_key=api_key),
        'default_model': 'gemini-pro'
    }
    # 未來可以添加其他服務
    # 'openai': {...},
    # 'claude': {...}
}
    # current_app.logger.warning("未找到 GOOGLE_API_KEY 環境變數。Gemini AI 服務可能無法使用。")

def get_ai_response(prompt: str, service_type: str = None, model_name: str = None, **kwargs) -> str:
    """根據指定的服務類型和提示，從 AI 模型獲取回應。

    Args:
        prompt (str): 提供給 AI 模型的提示文字。
        service_type (str, optional): 要使用的 AI 服務類型。
                                      如果為 None，則使用當前配置的服務。
        model_name (str, optional): 要使用的特定模型名稱。
                                     如果為 None，則使用服務的預設模型。
        **kwargs: 其他傳遞給特定 AI 服務 SDK 的參數。

    Returns:
        str: AI 模型生成的回應文字。

    Raises:
        ValueError: 如果指定的 service_type 不被支援，或者必要的 API 金鑰未設定。
        Exception: 如果 AI 服務呼叫過程中發生其他錯誤。
    """
    # 獲取配置管理器
    config_manager = get_ai_config_manager()

    # 如果未指定服務類型，使用當前配置的服務
    if service_type is None:
        service_type = config_manager.get_current_service()

    current_app.logger.info(f"請求 AI 服務: {service_type}, 模型: {model_name or '預設'}")

    if service_type.lower() == 'gemini':
        return _handle_gemini_request(prompt, model_name, config_manager, **kwargs)

    # elif service_type.lower() == 'openai':
    #     return _handle_openai_request(prompt, model_name, config_manager, **kwargs)

    # elif service_type.lower() == 'claude':
    #     return _handle_claude_request(prompt, model_name, config_manager, **kwargs)

    else:
        current_app.logger.error(f"不支援的 AI 服務類型: {service_type}")
        raise ValueError(f"不支援的 AI 服務類型: {service_type}。請選擇 'gemini' 或其他已支援的服務。")

def _handle_gemini_request(prompt: str, model_name: str, config_manager, **kwargs) -> str:
    """處理Gemini AI請求"""
    # 獲取API key
    api_key = config_manager.get_api_key('gemini')
    if not api_key:
        current_app.logger.error("Gemini API 金鑰未設定。")
        raise ValueError("Gemini API 金鑰未設定，無法使用此服務。請在設定中配置API金鑰。")

    try:
        # 配置Gemini API
        genai.configure(api_key=api_key)

        # 選擇模型，如果未指定，則使用預設模型
        service_config = config_manager.get_service_config('gemini')
        effective_model_name = model_name if model_name else service_config.get('model', 'gemini-pro')
        model = genai.GenerativeModel(effective_model_name)

        # 獲取生成設置
        gen_settings = config_manager.get_generation_settings()

        # 呼叫 Gemini API 生成內容
        response = model.generate_content(
            prompt,
            generation_config=genai.types.GenerationConfig(
                temperature=kwargs.get('creativity', kwargs.get('temperature', gen_settings.get('temperature', 0.7))),
                max_output_tokens=kwargs.get('max_tokens', gen_settings.get('max_tokens', 2048)),
                top_p=gen_settings.get('top_p', 0.9)
            )
        )

        # 檢查是否有回應文字
        if response.parts:
            generated_text = "".join(part.text for part in response.parts if hasattr(part, 'text'))
            if not generated_text and response.prompt_feedback and response.prompt_feedback.block_reason:
                block_reason_message = response.prompt_feedback.block_reason_message or str(response.prompt_feedback.block_reason)
                current_app.logger.warning(f"Gemini 內容生成被阻擋: {block_reason_message}")
                return f"(內容生成被阻擋: {block_reason_message})"
            current_app.logger.info(f"Gemini 成功生成回應。")
            return generated_text
        elif response.prompt_feedback and response.prompt_feedback.block_reason:
            block_reason_message = response.prompt_feedback.block_reason_message or str(response.prompt_feedback.block_reason)
            current_app.logger.warning(f"Gemini 內容生成因 prompt 被阻擋: {block_reason_message}")
            return f"(內容生成因 prompt 被阻擋: {block_reason_message})"
        else:
            current_app.logger.warning("Gemini 未返回任何內容，也沒有明確的阻擋原因。")
            return "(AI 未能生成回應)"

    except Exception as e:
        current_app.logger.error(f"呼叫 Gemini API 時發生錯誤: {e}")
        raise Exception(f"與 Gemini AI 服務通訊時發生錯誤。")

# 範例使用 (通常在路由處理函數中呼叫):
# if __name__ == '__main__':
#     # 這是用於直接測試此檔案的範例，需要模擬 Flask app 和 logger
#     class MockApp:
#         def __init__(self):
#             self.logger = logging.getLogger(__name__)
#             logging.basicConfig(level=logging.INFO)
#     current_app = MockApp() # 模擬 current_app

#     if GOOGLE_API_KEY:
#         try:
#             test_prompt = "請寫一個關於失落古城的簡短故事開頭。"
#             response_text = get_ai_response(test_prompt)
#             print("--- Gemini AI 回應 ---")
#             print(response_text)
#         except Exception as e:
#             print(f"測試 AI 服務時發生錯誤: {e}")
#     else:
#         print("請設定 GOOGLE_API_KEY 環境變數以測試 Gemini AI 服務。")