# core/routes.py
# 這個檔案包含了應用程式的主要路由定義。

from flask import Blueprint, render_template, request, jsonify, current_app
import os
import json
from .utils import load_data, save_data # 引入輔助函數
from .ai_services import get_ai_response # 引入 AI 服務函數
from .ai_config import get_ai_config_manager # 引入 AI 配置管理器

# 建立一個名為 'main' 的藍圖 (Blueprint)
# 藍圖是組織一組相關路由和視圖函數的方式
main_bp = Blueprint('main', __name__)

@main_bp.route('/')
def home():
    """應用程式首頁路由。
    
    渲染主頁面 index.html。
    """
    # render_template 會在 app.py 中設定的 templates 資料夾中尋找 HTML 檔案
    return render_template('index.html', title='AI 小說創作助手')

# --- 世界觀設定 (World Building) API --- 
@main_bp.route('/api/world', methods=['GET', 'POST'])
def world_settings():
    """處理世界觀設定的讀取和儲存。"""
    file_path = os.path.join(current_app.config['DATA_FOLDER'], 'worldview.json')
    if request.method == 'GET':
        # 讀取世界觀資料
        data = load_data(file_path, default_data={})
        return jsonify(data)
    elif request.method == 'POST':
        # 儲存世界觀資料
        if not request.is_json:
            return jsonify({'error': '請求必須是 JSON 格式'}), 400
        new_data = request.get_json()
        if save_data(file_path, new_data):
            return jsonify({'message': '世界觀設定已儲存'}), 200
        else:
            return jsonify({'error': '儲存世界觀設定失敗'}), 500

# --- 角色設定 (Character Workshop) API --- 
@main_bp.route('/api/characters', methods=['GET', 'POST'])
def character_settings():
    """處理角色設定的讀取和儲存。"""
    file_path = os.path.join(current_app.config['DATA_FOLDER'], 'characters.json')
    if request.method == 'GET':
        data = load_data(file_path, default_data=[]) # 角色通常是列表
        return jsonify(data)
    elif request.method == 'POST':
        if not request.is_json:
            return jsonify({'error': '請求必須是 JSON 格式'}), 400
        new_data = request.get_json()
        if save_data(file_path, new_data):
            return jsonify({'message': '角色設定已儲存'}), 200
        else:
            return jsonify({'error': '儲存角色設定失敗'}), 500

# --- 情節引擎 (Plot Engine) API --- 
@main_bp.route('/api/plot', methods=['GET', 'POST'])
def plot_settings():
    """處理情節設定的讀取和儲存。"""
    file_path = os.path.join(current_app.config['DATA_FOLDER'], 'storylines.json') # 假設情節檔案名
    if request.method == 'GET':
        data = load_data(file_path, default_data={})
        return jsonify(data)
    elif request.method == 'POST':
        if not request.is_json:
            return jsonify({'error': '請求必須是 JSON 格式'}), 400
        new_data = request.get_json()
        if save_data(file_path, new_data):
            return jsonify({'message': '情節設定已儲存'}), 200
        else:
            return jsonify({'error': '儲存情節設定失敗'}), 500

# --- AI 沙盒 (AI Sandbox) API --- 
@main_bp.route('/api/ai/sandbox_generate', methods=['POST'])
def ai_sandbox_generate():
    """處理 AI 沙盒的內容生成請求。"""
    if not request.is_json:
        return jsonify({'error': '請求必須是 JSON 格式'}), 400
    
    data = request.get_json()
    # 從請求中獲取 AI 服務類型 (例如 'gemini', 'openai' 等)
    # 以及使用者輸入的提示 (prompt)
    ai_service_type = data.get('ai_service', 'gemini') # 預設使用 gemini
    prompt = data.get('prompt')
    # 可以從前端傳遞更多上下文資訊，例如當前在哪個分頁 (tab_context)
    # tab_context = data.get('tab_context', 'general') 

    if not prompt:
        return jsonify({'error': '缺少 prompt 參數'}), 400

    try:
        # 呼叫 AI 服務函數進行內容生成
        # 未來可以根據 tab_context 調整傳遞給 AI 的系統訊息或上下文
        generated_text = get_ai_response(prompt, service_type=ai_service_type)
        return jsonify({'generated_text': generated_text})
    except Exception as e:
        # 記錄詳細錯誤，但只返回通用錯誤訊息給客戶端
        current_app.logger.error(f"AI Sandbox generation error: {e}")
        return jsonify({'error': f'AI 內容生成失敗: {str(e)}'}), 500

# --- 專案設定 (Project Settings) API --- 
@main_bp.route('/api/project/settings', methods=['GET', 'POST'])
def project_settings_api(): # 避免與之前的 project_settings 函數名衝突
    """處理專案整體設定的讀取和儲存 (例如：小說類型、風格等)。"""
    file_path = os.path.join(current_app.config['DATA_FOLDER'], 'project_settings.json')
    if request.method == 'GET':
        data = load_data(file_path, default_data={'genre': '奇幻', 'tone': '史詩'}) # 提供預設值
        return jsonify(data)
    elif request.method == 'POST':
        if not request.is_json:
            return jsonify({'error': '請求必須是 JSON 格式'}), 400
        new_data = request.get_json()
        if save_data(file_path, new_data):
            return jsonify({'message': '專案設定已儲存'}), 200
        else:
            return jsonify({'error': '儲存專案設定失敗'}), 500

# --- AI 整合生成 (Integrate & Generate) API ---
@main_bp.route('/api/ai/integrate_generate', methods=['POST'])
def ai_integrate_generate():
    """處理 AI 整合現有設定並生成內容的請求。"""
    if not request.is_json:
        return jsonify({'error': '請求必須是 JSON 格式'}), 400
    
    data = request.get_json()
    user_prompt = data.get('prompt')
    context = data.get('context') # 包含 world, characters, plot, projectSettings
    ai_service_type = data.get('ai_service', 'gemini')
    creativity = data.get('creativity', 0.7) # 可以從前端接收創意程度參數

    if not user_prompt:
        return jsonify({'error': '缺少 prompt 參數'}), 400
    if not context:
        return jsonify({'error': '缺少 context 參數'}), 400

    # 構建一個更豐富的提示給 AI，包含上下文資訊
    # 這裡的 prompt_for_ai 結構可以根據 AI 模型的要求進行調整
    # 例如，將 context 中的各個部分格式化為 AI 更易理解的文本
    # 為了簡化，我們先直接將 context 轉換為字串，但實際應用中可能需要更細緻的處理
    full_prompt = f"""基於以下設定，請回答使用者的問題或完成指令：

使用者指令：{user_prompt}

--- 世界觀 ---
{json.dumps(context.get('world', {}), ensure_ascii=False, indent=2)}

--- 角色 ---
{json.dumps(context.get('characters', []), ensure_ascii=False, indent=2)}

--- 情節 ---
{json.dumps(context.get('plot', {}), ensure_ascii=False, indent=2)}

--- 專案設定 ---
{json.dumps(context.get('projectSettings', {}), ensure_ascii=False, indent=2)}

請根據上述所有資訊進行生成。
"""

    try:
        # 呼叫 AI 服務函數進行內容生成
        # 注意：get_ai_response 可能需要調整以接受更複雜的上下文或不同的參數結構
        generated_text = get_ai_response(full_prompt, service_type=ai_service_type, creativity=creativity)
        return jsonify({'generated_text': generated_text})
    except Exception as e:
        current_app.logger.error(f"AI Integrate & Generate error: {e}")
        return jsonify({'error': f'AI 整合生成失敗: {str(e)}'}), 500

# --- AI 配置管理 API ---
@main_bp.route('/api/ai/config', methods=['GET', 'POST'])
def ai_config_api():
    """處理AI服務配置的讀取和設定"""
    config_manager = get_ai_config_manager()

    if request.method == 'GET':
        # 返回當前配置信息
        return jsonify({
            'current_service': config_manager.get_current_service(),
            'services': config_manager.get_all_services(),
            'available_services': config_manager.get_available_services(),
            'generation_settings': config_manager.get_generation_settings()
        })

    elif request.method == 'POST':
        if not request.is_json:
            return jsonify({'error': '請求必須是 JSON 格式'}), 400

        data = request.get_json()
        action = data.get('action')

        try:
            if action == 'set_current_service':
                service_name = data.get('service_name')
                if config_manager.set_current_service(service_name):
                    return jsonify({'message': f'已切換到 {service_name} 服務'})
                else:
                    return jsonify({'error': '無效的服務名稱'}), 400

            elif action == 'set_api_key':
                service_name = data.get('service_name')
                api_key = data.get('api_key', '').strip()
                if config_manager.set_api_key(service_name, api_key):
                    return jsonify({'message': f'{service_name} API金鑰已更新'})
                else:
                    return jsonify({'error': '無效的服務名稱'}), 400

            elif action == 'enable_service':
                service_name = data.get('service_name')
                enabled = data.get('enabled', True)
                if config_manager.enable_service(service_name, enabled):
                    status = '啟用' if enabled else '禁用'
                    return jsonify({'message': f'{service_name} 服務已{status}'})
                else:
                    return jsonify({'error': '無效的服務名稱'}), 400

            elif action == 'update_generation_settings':
                settings = data.get('settings', {})
                if config_manager.update_generation_settings(settings):
                    return jsonify({'message': '生成設定已更新'})
                else:
                    return jsonify({'error': '更新設定失敗'}), 500

            else:
                return jsonify({'error': '無效的操作'}), 400

        except Exception as e:
            current_app.logger.error(f"AI config error: {e}")
            return jsonify({'error': f'配置操作失敗: {str(e)}'}), 500

@main_bp.route('/api/ai/test', methods=['POST'])
def test_ai_service():
    """測試AI服務連接"""
    if not request.is_json:
        return jsonify({'error': '請求必須是 JSON 格式'}), 400

    data = request.get_json()
    service_name = data.get('service_name')

    config_manager = get_ai_config_manager()

    try:
        # 檢查服務是否可用
        if not config_manager.is_service_available(service_name):
            return jsonify({
                'success': False,
                'error': f'{service_name} 服務不可用或API金鑰未設定'
            })

        # 發送測試請求
        test_prompt = "請回覆'測試成功'來確認連接正常。"
        response = get_ai_response(test_prompt, service_type=service_name)

        return jsonify({
            'success': True,
            'message': f'{service_name} 服務連接正常',
            'test_response': response[:100] + '...' if len(response) > 100 else response
        })

    except Exception as e:
        current_app.logger.error(f"AI service test error: {e}")
        return jsonify({
            'success': False,
            'error': f'測試失敗: {str(e)}'
        })

# 可以在此處添加更多路由，例如：
# - 處理使用者身份驗證 (如果未來需要多使用者)
# - 匯出/匯入專案資料等