/* Basic Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.6;
    background-color: #f0f2f5; /* Light grey background */
    color: #333;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.container {
    display: flex;
    flex-grow: 1;
    max-width: 1400px; /* Max width for larger screens */
    margin: 20px auto; /* Centering the container */
    background-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    overflow: hidden; /* Ensures child elements adhere to border radius */
}

/* Sidebar Navigation */
.sidebar {
    width: 220px;
    background-color: #2c3e50; /* Dark blue-grey */
    color: #ecf0f1; /* Light grey text */
    padding: 20px 0;
}

.sidebar ul {
    list-style-type: none;
}

.sidebar ul li {
    padding: 12px 20px;
    cursor: pointer;
    border-left: 4px solid transparent;
    transition: background-color 0.3s ease, border-left-color 0.3s ease;
}

.sidebar ul li:hover {
    background-color: #34495e; /* Slightly lighter dark blue-grey */
}

.sidebar ul li.active {
    background-color: #1abc9c; /* Teal for active tab */
    border-left-color: #16a085; /* Darker teal */
    color: #ffffff;
    font-weight: bold;
}

/* Main Content Area */
.main-content {
    flex-grow: 1;
    padding: 25px;
    overflow-y: auto; /* Scroll for content overflow */
    background-color: #ffffff;
}

.tab-content {
    display: none; /* Hidden by default */
}

.tab-content.active {
    display: block; /* Show active tab content */
}

.tab-content h2 {
    color: #2c3e50; /* Dark blue-grey */
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #ecf0f1; /* Light grey border */
}

/* AI Preview Panel */
.ai-preview-panel {
    width: 300px;
    background-color: #f8f9fa; /* Very light grey */
    padding: 20px;
    border-left: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
}

.ai-preview-panel h3 {
    color: #34495e; /* Darker blue-grey */
    margin-bottom: 15px;
}

.ai-preview-panel textarea {
    width: 100%;
    min-height: 150px;
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: monospace;
    resize: vertical;
}

.ai-preview-panel button {
    background-color: #3498db; /* Blue */
    color: white;
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    margin-bottom: 10px;
    transition: background-color 0.3s ease;
}

.ai-preview-panel button:hover {
    background-color: #2980b9; /* Darker blue */
}

.ai-preview-panel #fill-back-settings {
    background-color: #2ecc71; /* Green */
}

.ai-preview-panel #fill-back-settings:hover {
    background-color: #27ae60; /* Darker green */
}

#completeness-check-results {
    margin-top: 10px;
    font-size: 0.9em;
    padding: 10px;
    background-color: #e9ecef;
    border-radius: 4px;
}

/* Global Controls Footer */
.global-controls {
    background-color: #34495e; /* Dark blue-grey */
    color: white;
    padding: 15px 20px;
    text-align: right;
    border-top: 1px solid #4a627a;
}

.global-controls button {
    background-color: #1abc9c; /* Teal */
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    margin-left: 10px;
    transition: background-color 0.3s ease;
}

.global-controls button:hover {
    background-color: #16a085; /* Darker teal */
}

/* General Form Elements (can be expanded in each tab) */
label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
}

input[type="text"],
input[type="number"],
textarea,
select {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;
    background-color: #fff;
}

textarea {
    min-height: 80px;
    resize: vertical;
}

.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 10px;
    margin-bottom: 15px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 0.95rem;
    background-color: #fff;
    transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    outline: none;
}

.form-hint {
    font-size: 0.85em;
    color: #6c757d;
    margin-top: -10px;
    margin-bottom: 15px;
}

fieldset {
    border: 1px solid #dee2e6;
    padding: 20px;
    margin-bottom: 25px;
    border-radius: 6px;
    background-color: #fdfdfd;
}

legend {
    font-size: 1.1em;
    font-weight: 600;
    color: #343a40;
    padding: 0 10px;
    margin-left: 10px; /* Aligns legend with fieldset padding */
}

/* Character and Scene Cards */
.character-card,
.scene-card {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.character-card h3,
.scene-card h3 {
    color: #1abc9c; /* Teal */
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.add-character-btn,
.remove-character-btn,
#add-scene-btn,
.remove-scene-btn {
    background-color: #3498db; /* Blue */
    color: white;
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
    margin-right: 5px;
    margin-bottom: 10px;
    transition: background-color 0.3s ease;
}

.remove-character-btn,
.remove-scene-btn {
    background-color: #e74c3c; /* Red */
}

.add-character-btn:hover,
#add-scene-btn:hover {
    background-color: #2980b9; /* Darker blue */
}

.remove-character-btn:hover,
.remove-scene-btn:hover {
    background-color: #c0392b; /* Darker red */
}

/* AI Creativity Slider in Rulebook */
.form-range {
    width: calc(100% - 40px); /* Adjust width to make space for value display */
    display: inline-block;
    vertical-align: middle;
}

#ai-creativity-value {
    display: inline-block;
    width: 30px;
    text-align: right;
    font-weight: bold;
    color: #3498db;
    vertical-align: middle;
}

/* Responsive adjustments */
@media (max-width: 992px) { /* Tablets and smaller */
    .container {
        flex-direction: column;
        margin: 0;
        border-radius: 0;
        box-shadow: none;
    }
    .sidebar {
        width: 100%;
        padding: 10px 0;
        display: flex; /* Horizontal tabs on small screens */
        overflow-x: auto;
    }
    .sidebar ul {
        display: flex;
        flex-direction: row;
        width: 100%;
    }
    .sidebar ul li {
        padding: 10px 15px;
        border-left: none;
        border-bottom: 4px solid transparent;
        white-space: nowrap; /* Prevent tab text wrapping */
    }
    .sidebar ul li.active {
        border-left: none;
        border-bottom-color: #16a085;
    }
    .ai-preview-panel {
        width: 100%;
        border-left: none;
        border-top: 1px solid #e0e0e0;
    }
}

@media (max-width: 600px) { /* Mobile phones */
    .main-content, .ai-preview-panel {
        padding: 15px;
    }
    .global-controls {
        padding: 10px;
        text-align: center;
    }
    .global-controls button {
        width: 100%;
        margin-left: 0;
        margin-bottom: 10px;
    }
    .global-controls button:last-child {
        margin-bottom: 0;
    }
}

/* === 新增樣式：Toast通知系統 === */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    min-width: 300px;
    padding: 15px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
}

.toast.show {
    transform: translateX(0);
}

.toast-success {
    background-color: #2ecc71;
}

.toast-error {
    background-color: #e74c3c;
}

.toast-warning {
    background-color: #f39c12;
}

.toast-info {
    background-color: #3498db;
}

/* === 動態卡片容器樣式 === */
#character-list,
#scene-list {
    margin-top: 20px;
}

.character-card,
.scene-card {
    position: relative;
    transition: box-shadow 0.2s ease;
}

.character-card:hover,
.scene-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.character-card h4,
.scene-card h4 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    color: #2c3e50;
    font-size: 1.1em;
}

.character-count,
.scene-count {
    background-color: #1abc9c;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: bold;
}

/* === 表單改進樣式 === */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

/* === 按鈕樣式改進 === */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.95rem;
    font-weight: 500;
    text-decoration: none;
    text-align: center;
    transition: all 0.2s ease;
    margin-right: 10px;
    margin-bottom: 10px;
}

.btn-primary {
    background-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-success {
    background-color: #2ecc71;
    color: white;
}

.btn-success:hover {
    background-color: #27ae60;
}

.btn-danger {
    background-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-secondary {
    background-color: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.85rem;
}

/* === 載入狀態樣式 === */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === AI沙盒面板改進 === */
.ai-sandbox-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.ai-sandbox-controls button {
    flex: 1;
    min-width: 120px;
}

/* === 頁籤導航改進 === */
.sidebar .tab-link {
    display: block;
    color: inherit;
    text-decoration: none;
    transition: all 0.2s ease;
}

.sidebar .tab-link:hover {
    text-decoration: none;
    color: inherit;
}

/* === 響應式改進 === */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .ai-sandbox-controls {
        flex-direction: column;
    }

    .ai-sandbox-controls button {
        width: 100%;
        margin-bottom: 10px;
    }

    .toast {
        left: 10px;
        right: 10px;
        min-width: auto;
    }
}

/* === AI設定頁籤專用樣式 === */
.ai-status-panel {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.ai-status-panel h3 {
    margin-top: 0;
    color: #495057;
}

#current-ai-status {
    font-family: monospace;
    background-color: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
}

.ai-service-card {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #ffffff;
    transition: border-color 0.2s ease;
}

.ai-service-card:hover {
    border-color: #3498db;
}

.ai-service-card.active {
    border-color: #1abc9c;
    background-color: #f0fffe;
}

.ai-service-card.unavailable {
    opacity: 0.6;
    background-color: #f8f9fa;
}

.service-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.service-name {
    font-weight: 600;
    color: #2c3e50;
}

.service-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 500;
}

.service-status.available {
    background-color: #d4edda;
    color: #155724;
}

.service-status.unavailable {
    background-color: #f8d7da;
    color: #721c24;
}

.service-status.current {
    background-color: #d1ecf1;
    color: #0c5460;
}

.service-description {
    color: #6c757d;
    font-size: 0.9em;
    margin-bottom: 10px;
}

.service-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.service-info {
    background-color: #f8f9fa;
    border-left: 4px solid #3498db;
    padding: 15px;
    margin-bottom: 15px;
}

.service-info h4 {
    margin-top: 0;
    color: #2c3e50;
}

.service-info a {
    color: #3498db;
    text-decoration: none;
}

.service-info a:hover {
    text-decoration: underline;
}

/* 範圍滑桿樣式 */
.form-range {
    width: calc(100% - 60px);
    margin-right: 10px;
}

.form-range + span {
    display: inline-block;
    width: 40px;
    text-align: center;
    font-weight: 600;
    color: #3498db;
}

/* API金鑰輸入框樣式 */
.api-key-group {
    display: flex;
    gap: 10px;
    align-items: flex-end;
}

.api-key-group .form-input {
    flex: 1;
    margin-bottom: 0;
}

.api-key-group button {
    margin-bottom: 0;
}

/* 狀態指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-indicator.online {
    background-color: #28a745;
}

.status-indicator.offline {
    background-color: #dc3545;
}

.status-indicator.testing {
    background-color: #ffc107;
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}