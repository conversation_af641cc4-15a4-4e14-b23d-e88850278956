/**
 * AI小說創作工具 - 前端JavaScript
 * 功能：頁籤切換、動態卡片管理、數據載入儲存、AI整合
 */

// === 應用程式狀態管理 ===
const appState = {
    world: {},
    characters: [],
    plot: {},
    projectSettings: {}
};

// === DOM載入完成後初始化 ===
document.addEventListener('DOMContentLoaded', () => {
    console.log('AI小說創作工具初始化中...');

    // DOM元素選取
    const tabLinks = document.querySelectorAll('.sidebar .tab-link');
    const tabContents = document.querySelectorAll('.main-content .tab-content');
    const aiSandboxOutput = document.getElementById('ai-sandbox-output');
    const sendToSandboxButton = document.getElementById('send-to-sandbox');
    const fillBackButton = document.getElementById('fill-back-settings');

    // 表單元素
    const worldBuildingForm = document.getElementById('world-building-form');
    const characterWorkshopForm = document.getElementById('character-workshop-form');
    const plotEngineForm = document.getElementById('plot-engine-form');

    // === 工具函數 ===
    /**
     * 顯示Toast通知
     * @param {string} message - 訊息內容
     * @param {string} type - 訊息類型 ('success', 'error', 'info', 'warning')
     */
    function showToast(message, type = 'info') {
        const toast = document.getElementById('toast-notification');
        const toastBody = toast.querySelector('.toast-body');

        if (toast && toastBody) {
            toastBody.textContent = message;
            toast.className = `toast show toast-${type}`;
            toast.style.display = 'block';

            // 3秒後自動隱藏
            setTimeout(() => {
                toast.style.display = 'none';
            }, 3000);
        } else {
            // 備用方案：使用alert
            alert(`[${type.toUpperCase()}] ${message}`);
        }

        if (type === 'error') {
            console.error(message);
        }
    }

    /**
     * 統一的API請求函數
     * @param {string} url - API端點URL
     * @param {string} method - HTTP方法
     * @param {object} body - 請求主體
     * @returns {Promise<object>} - 解析後的JSON回應
     */
    async function fetchData(url, method = 'GET', body = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
        };

        if (body) {
            options.body = JSON.stringify(body);
        }

        try {
            const response = await fetch(url, options);
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ error: '未知錯誤' }));
                throw new Error(errorData.error || `HTTP錯誤! 狀態: ${response.status}`);
            }
            return await response.json();
        } catch (error) {
            console.error('API請求失敗:', error);
            throw error;
        }
    }

    // === 數據管理函數 ===
    /**
     * 載入數據並更新狀態
     * @param {string} dataType - 數據類型
     * @param {string} apiUrl - API端點
     */
    async function loadData(dataType, apiUrl) {
        try {
            const data = await fetchData(apiUrl);
            appState[dataType] = data;
            renderData(dataType);
            showToast(`${dataType}數據載入成功`, 'success');
        } catch (error) {
            showToast(`載入${dataType}數據失敗: ${error.message}`, 'error');
        }
    }

    /**
     * 儲存數據到後端
     * @param {string} dataType - 數據類型
     * @param {string} apiUrl - API端點
     * @param {object} data - 要儲存的數據
     */
    async function saveData(dataType, apiUrl, data) {
        try {
            const result = await fetchData(apiUrl, 'POST', data);
            appState[dataType] = data;
            showToast(`${dataType}數據儲存成功`, 'success');
            return result;
        } catch (error) {
            showToast(`儲存${dataType}數據失敗: ${error.message}`, 'error');
            throw error;
        }
    }

    /**
     * 根據數據類型渲染對應的表單
     * @param {string} dataType - 數據類型
     */
    function renderData(dataType) {
        switch (dataType) {
            case 'world':
                if (worldBuildingForm) populateWorldForm(appState.world);
                break;
            case 'characters':
                if (characterWorkshopForm) populateCharactersForm(appState.characters);
                break;
            case 'plot':
                if (plotEngineForm) populatePlotForm(appState.plot);
                break;
        }
    }

    // === 頁籤切換功能 ===
    /**
     * 初始化頁籤切換功能
     */
    function initTabNavigation() {
        tabLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();

                // 移除所有active類別
                tabLinks.forEach(l => l.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));

                // 添加active類別到點擊的頁籤
                link.classList.add('active');
                const tabId = link.getAttribute('data-tab');
                const targetContent = document.getElementById(tabId);

                if (targetContent) {
                    targetContent.classList.add('active');
                    console.log(`切換到頁籤: ${tabId}`);
                }
            });
        });
    }

    // === 表單數據處理函數 ===
    /**
     * 填充世界觀表單
     * @param {object} data - 世界觀數據
     */
    function populateWorldForm(data) {
        if (!worldBuildingForm || !data) return;

        const fields = {
            'wb-main-concept': data.mainConcept || '',
            'wb-keywords': data.keywords || '',
            'wb-map-description': data.mapDescription || '',
            'wb-climate-terrain': data.climateTerrain || '',
            'wb-key-locations': data.keyLocations || '',
            'wb-factions': data.factions || '',
            'wb-culture-economy': data.cultureEconomy || '',
            'wb-tech-magic-source': data.techMagicSource || '',
            'wb-tech-magic-skills': data.techMagicSkills || '',
            'wb-tech-magic-rigor': data.techMagicRigor || '',
            'wb-history-events': data.historyEvents || ''
        };

        Object.entries(fields).forEach(([id, value]) => {
            const element = worldBuildingForm.querySelector(`#${id}`);
            if (element) {
                element.value = value;
            }
        });
    }

    /**
     * 收集世界觀表單數據
     * @returns {object} 世界觀數據
     */
    function collectWorldFormData() {
        if (!worldBuildingForm) return {};

        const data = {};
        const fields = [
            'wb-main-concept', 'wb-keywords', 'wb-map-description',
            'wb-climate-terrain', 'wb-key-locations', 'wb-factions',
            'wb-culture-economy', 'wb-tech-magic-source', 'wb-tech-magic-skills',
            'wb-tech-magic-rigor', 'wb-history-events'
        ];

        fields.forEach(fieldId => {
            const element = worldBuildingForm.querySelector(`#${fieldId}`);
            if (element) {
                const key = fieldId.replace('wb-', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                data[key] = element.value;
            }
        });

        return data;
    }

    // === 角色管理功能 ===
    let characterCount = 0;

    /**
     * 填充角色表單
     * @param {Array} data - 角色數據陣列
     */
    function populateCharactersForm(data) {
        if (!characterWorkshopForm) return;

        const characterList = characterWorkshopForm.querySelector('#character-list');
        if (!characterList) return;

        // 清空現有角色卡片
        characterList.innerHTML = '';
        characterCount = 0;

        if (data && Array.isArray(data) && data.length > 0) {
            data.forEach(charData => {
                addCharacterCard(charData);
            });
        } else {
            // 如果沒有數據，添加一個空的角色卡片
            addCharacterCard();
        }
    }

    /**
     * 收集角色表單數據
     * @returns {Array} 角色數據陣列
     */
    function collectCharactersFormData() {
        if (!characterWorkshopForm) return [];

        const characters = [];
        const characterCards = characterWorkshopForm.querySelectorAll('.character-card');

        characterCards.forEach(card => {
            const char = {};
            const fields = [
                'char-name', 'char-age', 'char-race-job', 'char-appearance',
                'char-goal', 'char-fear', 'char-belief', 'char-morality',
                'char-relations', 'char-arc'
            ];

            fields.forEach(fieldClass => {
                const element = card.querySelector(`.${fieldClass}`);
                if (element) {
                    const key = fieldClass.replace('char-', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                    char[key] = element.value;
                }
            });

            // 只添加有名字的角色
            if (char.name && char.name.trim()) {
                characters.push(char);
            }
        });

        return characters;
    }

    /**
     * 添加角色卡片
     * @param {object} charData - 角色數據（可選）
     */
    function addCharacterCard(charData = null) {
        const characterList = characterWorkshopForm?.querySelector('#character-list');
        const template = document.getElementById('character-card-template');

        if (!characterList || !template) {
            console.error('找不到角色列表容器或模板');
            return;
        }

        characterCount++;
        const clone = template.content.cloneNode(true);
        const card = clone.querySelector('.character-card');

        // 更新卡片ID和計數
        card.id = `character-card-${characterCount}`;
        const countSpan = card.querySelector('.character-count');
        if (countSpan) {
            countSpan.textContent = characterCount;
        }

        // 更新所有input和textarea的ID和name屬性
        const inputs = card.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            const baseName = input.name;
            input.id = `${baseName}-${characterCount}`;
            input.name = `${baseName}-${characterCount}`;
        });

        // 更新label的for屬性
        const labels = card.querySelectorAll('label');
        labels.forEach(label => {
            const forAttr = label.getAttribute('for');
            if (forAttr) {
                label.setAttribute('for', `${forAttr}-${characterCount}`);
            }
        });

        // 填充數據
        if (charData) {
            const fields = {
                'char-name': charData.name || '',
                'char-age': charData.age || '',
                'char-race-job': charData.raceJob || '',
                'char-appearance': charData.appearance || '',
                'char-goal': charData.goal || '',
                'char-fear': charData.fear || '',
                'char-belief': charData.belief || '',
                'char-morality': charData.morality || '',
                'char-relations': charData.relations || '',
                'char-arc': charData.arc || ''
            };

            Object.entries(fields).forEach(([className, value]) => {
                const element = card.querySelector(`.${className}`);
                if (element) {
                    element.value = value;
                }
            });
        }

        characterList.appendChild(card);
    }

    // === 情節管理功能 ===
    let sceneCount = 0;

    /**
     * 填充情節表單
     * @param {object} data - 情節數據
     */
    function populatePlotForm(data) {
        if (!plotEngineForm) return;

        // 填充基本情節信息
        const fields = {
            'plot-logline': data.logline || '',
            'plot-theme': data.theme || '',
            'plot-conflict': data.conflict || '',
            'plot-structure-template': data.structureTemplate || 'three-act',
            'plot-beats': data.beats || '',
            'plot-twists': data.twists || '',
            'plot-subplots': data.subplots || '',
            'plot-foreshadowing': data.foreshadowing || ''
        };

        Object.entries(fields).forEach(([id, value]) => {
            const element = plotEngineForm.querySelector(`#${id}`);
            if (element) {
                element.value = value;
            }
        });

        // 處理場景列表
        const sceneList = plotEngineForm.querySelector('#scene-list');
        if (sceneList) {
            sceneList.innerHTML = '';
            sceneCount = 0;

            if (data.scenes && Array.isArray(data.scenes) && data.scenes.length > 0) {
                data.scenes.forEach(sceneData => {
                    addSceneCard(sceneData);
                });
            } else {
                addSceneCard();
            }
        }
    }

    /**
     * 收集情節表單數據
     * @returns {object} 情節數據
     */
    function collectPlotFormData() {
        if (!plotEngineForm) return {};

        const data = {};
        const fields = [
            'plot-logline', 'plot-theme', 'plot-conflict', 'plot-structure-template',
            'plot-beats', 'plot-twists', 'plot-subplots', 'plot-foreshadowing'
        ];

        fields.forEach(fieldId => {
            const element = plotEngineForm.querySelector(`#${fieldId}`);
            if (element) {
                const key = fieldId.replace('plot-', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                data[key] = element.value;
            }
        });

        // 收集場景數據
        const scenes = [];
        const sceneCards = plotEngineForm.querySelectorAll('.scene-card');
        sceneCards.forEach(card => {
            const scene = {};
            const sceneFields = [
                'scene-title', 'scene-location', 'scene-time', 'scene-characters',
                'scene-event', 'scene-atmosphere', 'scene-dialogue-summary',
                'scene-plot-advancement'
            ];

            sceneFields.forEach(fieldClass => {
                const element = card.querySelector(`.${fieldClass}`);
                if (element) {
                    const key = fieldClass.replace('scene-', '').replace(/-([a-z])/g, (g) => g[1].toUpperCase());
                    scene[key] = element.value;
                }
            });

            if (scene.title && scene.title.trim()) {
                scenes.push(scene);
            }
        });

        data.scenes = scenes;
        return data;
    }

    /**
     * 添加場景卡片
     * @param {object} sceneData - 場景數據（可選）
     */
    function addSceneCard(sceneData = null) {
        const sceneList = plotEngineForm?.querySelector('#scene-list');
        const template = document.getElementById('scene-card-template');

        if (!sceneList || !template) {
            console.error('找不到場景列表容器或模板');
            return;
        }

        sceneCount++;
        const clone = template.content.cloneNode(true);
        const card = clone.querySelector('.scene-card');

        // 更新卡片ID和計數
        card.id = `scene-card-${sceneCount}`;
        const countSpan = card.querySelector('.scene-count');
        if (countSpan) {
            countSpan.textContent = sceneCount;
        }

        // 更新所有input和textarea的ID和name屬性
        const inputs = card.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            const baseName = input.name;
            input.id = `${baseName}-${sceneCount}`;
            input.name = `${baseName}-${sceneCount}`;
        });

        // 更新label的for屬性
        const labels = card.querySelectorAll('label');
        labels.forEach(label => {
            const forAttr = label.getAttribute('for');
            if (forAttr) {
                label.setAttribute('for', `${forAttr}-${sceneCount}`);
            }
        });

        // 填充數據
        if (sceneData) {
            const fields = {
                'scene-title': sceneData.title || '',
                'scene-location': sceneData.location || '',
                'scene-time': sceneData.time || '',
                'scene-characters': sceneData.characters || '',
                'scene-event': sceneData.event || '',
                'scene-atmosphere': sceneData.atmosphere || '',
                'scene-dialogue-summary': sceneData.dialogueSummary || '',
                'scene-plot-advancement': sceneData.plotAdvancement || ''
            };

            Object.entries(fields).forEach(([className, value]) => {
                const element = card.querySelector(`.${className}`);
                if (element) {
                    element.value = value;
                }
            });
        }

        sceneList.appendChild(card);
    }

    // === 事件監聽器設置 ===
    /**
     * 初始化動態卡片事件監聽器
     */
    function initDynamicCardEvents() {
        // 角色卡片事件
        if (characterWorkshopForm) {
            characterWorkshopForm.addEventListener('click', (e) => {
                if (e.target.classList.contains('add-character-btn')) {
                    addCharacterCard();
                } else if (e.target.classList.contains('remove-character-btn')) {
                    const characterList = characterWorkshopForm.querySelector('#character-list');
                    const cards = characterList?.querySelectorAll('.character-card');

                    if (cards && cards.length > 1) {
                        e.target.closest('.character-card').remove();
                        showToast('角色卡片已移除', 'info');
                    } else {
                        showToast('至少需要保留一個角色卡片', 'warning');
                    }
                }
            });
        }

        // 場景卡片事件
        if (plotEngineForm) {
            plotEngineForm.addEventListener('click', (e) => {
                if (e.target.classList.contains('add-scene-btn')) {
                    addSceneCard();
                } else if (e.target.classList.contains('remove-scene-btn')) {
                    const sceneList = plotEngineForm.querySelector('#scene-list');
                    const cards = sceneList?.querySelectorAll('.scene-card');

                    if (cards && cards.length > 1) {
                        e.target.closest('.scene-card').remove();
                        showToast('場景卡片已移除', 'info');
                    } else {
                        showToast('至少需要保留一個場景卡片', 'warning');
                    }
                }
            });
        }
    }

    // === AI沙盒功能 ===
    /**
     * 初始化AI沙盒功能
     */
    function initAISandbox() {
        if (sendToSandboxButton && aiSandboxOutput) {
            sendToSandboxButton.addEventListener('click', async () => {
                const prompt = prompt('請輸入您想讓AI生成的內容提示：');
                if (!prompt) return;

                aiSandboxOutput.value = 'AI正在生成中...請稍候...';

                try {
                    const result = await fetchData('/api/ai/sandbox_generate', 'POST', {
                        prompt: prompt,
                        ai_service: 'gemini'
                    });

                    aiSandboxOutput.value = result.generated_text || 'AI未返回有效內容';
                    showToast('AI內容生成完成', 'success');
                } catch (error) {
                    aiSandboxOutput.value = `AI生成失敗: ${error.message}`;
                    showToast(`AI生成失敗: ${error.message}`, 'error');
                }
            });
        }

        if (fillBackButton && aiSandboxOutput) {
            fillBackButton.addEventListener('click', () => {
                const generatedText = aiSandboxOutput.value;
                if (!generatedText || generatedText.includes('AI正在生成中') || generatedText.includes('AI生成失敗')) {
                    showToast('沙盒中沒有可回填的內容', 'warning');
                    return;
                }

                const activeTab = document.querySelector('.tab-content.active');
                if (!activeTab) {
                    showToast('沒有活動的分頁可供回填', 'error');
                    return;
                }

                const targetTextarea = activeTab.querySelector('textarea');
                if (targetTextarea) {
                    targetTextarea.value += (targetTextarea.value ? '\n\n--- AI生成內容 ---\n' : '') + generatedText;
                    showToast('內容已回填到當前分頁', 'success');
                } else {
                    showToast('在當前分頁未找到可回填的文字區域', 'warning');
                }
            });
        }
    }

    // === 數據自動儲存功能 ===
    /**
     * 設置自動儲存功能
     */
    function setupAutoSave() {
        const forms = [
            { form: worldBuildingForm, type: 'world', url: '/api/world', collectFn: collectWorldFormData },
            { form: characterWorkshopForm, type: 'characters', url: '/api/characters', collectFn: collectCharactersFormData },
            { form: plotEngineForm, type: 'plot', url: '/api/plot', collectFn: collectPlotFormData }
        ];

        forms.forEach(({ form, type, url, collectFn }) => {
            if (form) {
                // 監聽表單變更事件，延遲儲存
                let saveTimeout;
                form.addEventListener('input', () => {
                    clearTimeout(saveTimeout);
                    saveTimeout = setTimeout(async () => {
                        try {
                            const data = collectFn();
                            await saveData(type, url, data);
                        } catch (error) {
                            console.error(`自動儲存${type}失敗:`, error);
                        }
                    }, 2000); // 2秒後自動儲存
                });
            }
        });
    }

    // === 初始化函數 ===
    /**
     * 載入初始數據
     */
    async function loadInitialData() {
        const dataTypes = [
            { type: 'world', url: '/api/world' },
            { type: 'characters', url: '/api/characters' },
            { type: 'plot', url: '/api/plot' }
        ];

        for (const { type, url } of dataTypes) {
            try {
                await loadData(type, url);
            } catch (error) {
                console.error(`載入${type}數據失敗:`, error);
            }
        }
    }

    // === 主初始化函數 ===
    /**
     * 應用程式主初始化
     */
    async function initApp() {
        try {
            console.log('開始初始化AI小說創作工具...');

            // 初始化各個功能模組
            initTabNavigation();
            initDynamicCardEvents();
            initAISandbox();
            setupAutoSave();

            // 載入初始數據
            await loadInitialData();

            console.log('AI小說創作工具初始化完成！');
            showToast('應用程式載入完成', 'success');

        } catch (error) {
            console.error('初始化失敗:', error);
            showToast('應用程式初始化失敗', 'error');
        }
    }

    // === AI設定管理功能 ===
    /**
     * 初始化AI設定頁籤功能
     */
    function initAISettings() {
        const aiConfigForm = document.getElementById('ai-config-form');
        const aiServiceSelect = document.getElementById('ai-service-select');
        const apiKeyInput = document.getElementById('api-key-input');
        const toggleVisibilityBtn = document.getElementById('toggle-api-key-visibility');
        const saveApiKeyBtn = document.getElementById('save-api-key');
        const testConnectionBtn = document.getElementById('test-ai-connection');
        const temperatureSlider = document.getElementById('ai-temperature');
        const temperatureValue = document.getElementById('temperature-value');
        const topPSlider = document.getElementById('ai-top-p');
        const topPValue = document.getElementById('top-p-value');
        const saveSettingsBtn = document.getElementById('save-generation-settings');
        const resetSettingsBtn = document.getElementById('reset-generation-settings');

        if (!aiConfigForm) return;

        // 載入AI配置
        loadAIConfig();

        // API金鑰可見性切換
        if (toggleVisibilityBtn && apiKeyInput) {
            toggleVisibilityBtn.addEventListener('click', () => {
                if (apiKeyInput.type === 'password') {
                    apiKeyInput.type = 'text';
                    toggleVisibilityBtn.textContent = '隱藏';
                } else {
                    apiKeyInput.type = 'password';
                    toggleVisibilityBtn.textContent = '顯示';
                }
            });
        }

        // 服務選擇變更
        if (aiServiceSelect && apiKeyInput) {
            aiServiceSelect.addEventListener('change', async () => {
                const serviceName = aiServiceSelect.value;
                if (serviceName) {
                    try {
                        const config = await fetchData('/api/ai/config');
                        const serviceConfig = config.services[serviceName];
                        if (serviceConfig) {
                            apiKeyInput.value = serviceConfig.api_key || '';
                        }
                    } catch (error) {
                        console.error('載入服務配置失敗:', error);
                    }
                }
            });
        }

        // 儲存API金鑰
        if (saveApiKeyBtn) {
            saveApiKeyBtn.addEventListener('click', async () => {
                const serviceName = aiServiceSelect?.value;
                const apiKey = apiKeyInput?.value?.trim();

                if (!serviceName) {
                    showToast('請選擇AI服務', 'warning');
                    return;
                }

                try {
                    await fetchData('/api/ai/config', 'POST', {
                        action: 'set_api_key',
                        service_name: serviceName,
                        api_key: apiKey
                    });

                    showToast('API金鑰已儲存', 'success');
                    loadAIConfig(); // 重新載入配置
                } catch (error) {
                    showToast(`儲存失敗: ${error.message}`, 'error');
                }
            });
        }

        // 測試連接
        if (testConnectionBtn) {
            testConnectionBtn.addEventListener('click', async () => {
                const serviceName = aiServiceSelect?.value;

                if (!serviceName) {
                    showToast('請選擇AI服務', 'warning');
                    return;
                }

                testConnectionBtn.disabled = true;
                testConnectionBtn.textContent = '測試中...';

                try {
                    const result = await fetchData('/api/ai/test', 'POST', {
                        service_name: serviceName
                    });

                    if (result.success) {
                        showToast(result.message, 'success');
                    } else {
                        showToast(result.error, 'error');
                    }
                } catch (error) {
                    showToast(`測試失敗: ${error.message}`, 'error');
                } finally {
                    testConnectionBtn.disabled = false;
                    testConnectionBtn.textContent = '測試連接';
                }
            });
        }

        // 滑桿值更新
        if (temperatureSlider && temperatureValue) {
            temperatureSlider.addEventListener('input', () => {
                temperatureValue.textContent = temperatureSlider.value;
            });
        }

        if (topPSlider && topPValue) {
            topPSlider.addEventListener('input', () => {
                topPValue.textContent = topPSlider.value;
            });
        }

        // 儲存生成設定
        if (saveSettingsBtn) {
            saveSettingsBtn.addEventListener('click', async () => {
                const settings = {
                    temperature: parseFloat(temperatureSlider?.value || 0.7),
                    max_tokens: parseInt(document.getElementById('ai-max-tokens')?.value || 2048),
                    top_p: parseFloat(topPSlider?.value || 0.9)
                };

                try {
                    await fetchData('/api/ai/config', 'POST', {
                        action: 'update_generation_settings',
                        settings: settings
                    });

                    showToast('生成設定已儲存', 'success');
                } catch (error) {
                    showToast(`儲存失敗: ${error.message}`, 'error');
                }
            });
        }

        // 重置設定
        if (resetSettingsBtn) {
            resetSettingsBtn.addEventListener('click', () => {
                if (temperatureSlider) temperatureSlider.value = 0.7;
                if (temperatureValue) temperatureValue.textContent = '0.7';
                if (topPSlider) topPSlider.value = 0.9;
                if (topPValue) topPValue.textContent = '0.9';

                const maxTokensInput = document.getElementById('ai-max-tokens');
                if (maxTokensInput) maxTokensInput.value = 2048;

                showToast('設定已重置為預設值', 'info');
            });
        }
    }

    /**
     * 載入AI配置
     */
    async function loadAIConfig() {
        try {
            const config = await fetchData('/api/ai/config');

            // 更新當前狀態顯示
            updateAIStatusDisplay(config);

            // 更新服務列表
            updateAIServicesList(config);

            // 更新服務選擇下拉選單
            updateServiceSelect(config);

            // 更新生成設定
            updateGenerationSettings(config.generation_settings);

        } catch (error) {
            console.error('載入AI配置失敗:', error);
            showToast('載入AI配置失敗', 'error');
        }
    }

    /**
     * 更新AI狀態顯示
     */
    function updateAIStatusDisplay(config) {
        const statusDiv = document.getElementById('current-ai-status');
        if (!statusDiv) return;

        const currentService = config.current_service;
        const serviceConfig = config.services[currentService];
        const hasApiKey = serviceConfig && serviceConfig.api_key;

        statusDiv.innerHTML = `
            <p><strong>當前服務:</strong> ${serviceConfig?.name || currentService}</p>
            <p><strong>狀態:</strong>
                <span class="status-indicator ${hasApiKey ? 'online' : 'offline'}"></span>
                ${hasApiKey ? '已配置' : '未配置API金鑰'}
            </p>
            <p><strong>模型:</strong> ${serviceConfig?.model || '未知'}</p>
        `;
    }

    /**
     * 更新AI服務列表
     */
    function updateAIServicesList(config) {
        const servicesList = document.getElementById('ai-services-list');
        if (!servicesList) return;

        servicesList.innerHTML = '';

        Object.entries(config.services).forEach(([serviceName, serviceConfig]) => {
            const isAvailable = config.available_services.find(s => s.name === serviceName);
            const isCurrent = serviceName === config.current_service;

            const serviceCard = document.createElement('div');
            serviceCard.className = `ai-service-card ${isCurrent ? 'active' : ''} ${!isAvailable?.has_api_key ? 'unavailable' : ''}`;

            serviceCard.innerHTML = `
                <div class="service-header">
                    <span class="service-name">${serviceConfig.name}</span>
                    <span class="service-status ${isCurrent ? 'current' : (isAvailable?.has_api_key ? 'available' : 'unavailable')}">
                        ${isCurrent ? '當前使用' : (isAvailable?.has_api_key ? '可用' : '未配置')}
                    </span>
                </div>
                <div class="service-description">${serviceConfig.description}</div>
                <div class="service-actions">
                    ${!isCurrent && isAvailable?.has_api_key ?
                        `<button class="btn btn-sm btn-primary" onclick="switchAIService('${serviceName}')">切換使用</button>` :
                        ''
                    }
                    <button class="btn btn-sm btn-secondary" onclick="configureService('${serviceName}')">配置</button>
                </div>
            `;

            servicesList.appendChild(serviceCard);
        });
    }

    /**
     * 更新服務選擇下拉選單
     */
    function updateServiceSelect(config) {
        const select = document.getElementById('ai-service-select');
        if (!select) return;

        select.innerHTML = '<option value="">請選擇AI服務</option>';

        Object.entries(config.services).forEach(([serviceName, serviceConfig]) => {
            const option = document.createElement('option');
            option.value = serviceName;
            option.textContent = serviceConfig.name;
            if (serviceName === config.current_service) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    }

    /**
     * 更新生成設定
     */
    function updateGenerationSettings(settings) {
        const temperatureSlider = document.getElementById('ai-temperature');
        const temperatureValue = document.getElementById('temperature-value');
        const topPSlider = document.getElementById('ai-top-p');
        const topPValue = document.getElementById('top-p-value');
        const maxTokensInput = document.getElementById('ai-max-tokens');

        if (temperatureSlider && settings.temperature !== undefined) {
            temperatureSlider.value = settings.temperature;
            if (temperatureValue) temperatureValue.textContent = settings.temperature;
        }

        if (topPSlider && settings.top_p !== undefined) {
            topPSlider.value = settings.top_p;
            if (topPValue) topPValue.textContent = settings.top_p;
        }

        if (maxTokensInput && settings.max_tokens !== undefined) {
            maxTokensInput.value = settings.max_tokens;
        }
    }

    // 全域函數供HTML調用
    window.switchAIService = async function(serviceName) {
        try {
            await fetchData('/api/ai/config', 'POST', {
                action: 'set_current_service',
                service_name: serviceName
            });

            showToast(`已切換到 ${serviceName} 服務`, 'success');
            loadAIConfig(); // 重新載入配置
        } catch (error) {
            showToast(`切換失敗: ${error.message}`, 'error');
        }
    };

    window.configureService = function(serviceName) {
        const select = document.getElementById('ai-service-select');
        if (select) {
            select.value = serviceName;
            select.dispatchEvent(new Event('change'));
        }

        // 滾動到配置區域
        const configSection = document.querySelector('fieldset legend:contains("API金鑰設定")');
        if (configSection) {
            configSection.scrollIntoView({ behavior: 'smooth' });
        }
    };

    // 執行初始化
    initApp();

    // 當切換到AI設定頁籤時初始化AI設定功能
    const aiSettingsTab = document.querySelector('[data-tab="ai-settings"]');
    if (aiSettingsTab) {
        aiSettingsTab.addEventListener('click', () => {
            setTimeout(() => {
                initAISettings();
            }, 100); // 稍微延遲以確保頁籤切換完成
        });
    }

// === 結束DOMContentLoaded事件監聽器 ===
});


