<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI輔助小說創作工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <!-- 左側：垂直頁籤導覽列 -->
        <nav class="sidebar">
            <ul>
                <li class="tab-link active" data-tab="world-building">世界構建 🌍</li>
                <li class="tab-link" data-tab="character-workshop">角色工房 👤</li>
                <li class="tab-link" data-tab="plot-engine">情節引擎 📜</li>
                <li class="tab-link" data-tab="style-palette">風格調色板 🎨</li>
                <li class="tab-link" data-tab="scene-library">場景庫 🏰</li>
                <li class="tab-link" data-tab="rulebook">規則手冊 📖</li>
                <li class="tab-link" data-tab="ai-settings">AI設定 🤖</li>
            </ul>
        </nav>

        <!-- 中央：動態編輯面板 -->
        <main class="main-content">
            <div id="world-building" class="tab-content active">
                <h2>世界構建 🌍</h2>
                <form id="world-building-form">
                    <fieldset>
                        <legend>核心概念</legend>
                        <div>
                            <label for="wb-main-concept">一句話主旨:</label>
                            <input type="text" id="wb-main-concept" name="wb-main-concept" class="form-input">
                        </div>
                        <div>
                            <label for="wb-keywords">關鍵詞 (以逗號分隔):</label>
                            <input type="text" id="wb-keywords" name="wb-keywords" class="form-input" placeholder="例如：賽博龐克, 階級固化, 意識上傳">
                        </div>
                    </fieldset>

                    <fieldset>
                        <legend>物理/地理</legend>
                        <div>
                            <label for="wb-map-description">星球/大陸/城市地圖描述:</label>
                            <textarea id="wb-map-description" name="wb-map-description" rows="3" class="form-textarea" placeholder="描述地圖的整體樣貌和重要區域"></textarea>
                        </div>
                        <div>
                            <label for="wb-climate-terrain">氣候與地貌:</label>
                            <textarea id="wb-climate-terrain" name="wb-climate-terrain" rows="3" class="form-textarea" placeholder="描述主要的氣候類型和地理特徵"></textarea>
                        </div>
                        <div>
                            <label for="wb-key-locations">關鍵地點 (重要性描述):</label>
                            <textarea id="wb-key-locations" name="wb-key-locations" rows="3" class="form-textarea" placeholder="列出關鍵地點及其重要性，例如：首都 - 政治中心；禁地 - 蘊藏古老秘密"></textarea>
                        </div>
                    </fieldset>

                    <fieldset>
                        <legend>社會結構</legend>
                        <div>
                            <label for="wb-factions">勢力組織 (名稱,目標,領袖,標誌;關係):</label>
                            <textarea id="wb-factions" name="wb-factions" rows="4" class="form-textarea" placeholder="每行一個勢力，例如：銀翼財團,壟斷科技,CEO艾拉,鷹翼標誌;敵對:暗影兄弟會"></textarea>
                        </div>
                        <div>
                            <label for="wb-culture-economy">文化習俗與經濟體系:</label>
                            <textarea id="wb-culture-economy" name="wb-culture-economy" rows="3" class="form-textarea" placeholder="描述主要的文化習俗、節日、禁忌、禮儀，以及貨幣、主要資源和經濟模式"></textarea>
                        </div>
                    </fieldset>

                    <fieldset>
                        <legend>科技/魔法體系</legend>
                        <div>
                            <label for="wb-tech-magic-source">能量來源、使用限制、代價:</label>
                            <textarea id="wb-tech-magic-source" name="wb-tech-magic-source" rows="3" class="form-textarea" placeholder="描述科技或魔法的能量來源，使用上有何限制，以及使用時可能付出的代價"></textarea>
                        </div>
                        <div>
                            <label for="wb-tech-magic-skills">常見技能/設備:</label>
                            <textarea id="wb-tech-magic-skills" name="wb-tech-magic-skills" rows="3" class="form-textarea" placeholder="列出常見的科技設備或魔法技能"></textarea>
                        </div>
                        <div>
                            <label for="wb-tech-magic-rigor">嚴謹度:</label>
                            <input type="text" id="wb-tech-magic-rigor" name="wb-tech-magic-rigor" class="form-input" placeholder="例如：硬核科幻、低魔幻想、軟科幻、高魔"></textarea>
                        </div>
                    </fieldset>

                    <fieldset>
                        <legend>歷史事件</legend>
                        <div>
                            <label for="wb-history-events">關鍵歷史事件節點:</label>
                            <textarea id="wb-history-events" name="wb-history-events" rows="4" class="form-textarea" placeholder="列出重要的歷史事件及其簡要描述，例如：千年戰爭 - 確立現今勢力格局；大崩壞 - 舊文明毀滅"></textarea>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div id="character-workshop" class="tab-content">
                <h2>角色工房 👤</h2>
                <form id="character-workshop-form">
                    <fieldset>
                        <legend>角色卡片系統</legend>
                        <div id="character-list">
                            <div class="character-card">
                            <h3>角色1</h3> <button type="button" class="add-character-btn">新增角色</button> <button type="button" class="remove-character-btn">移除此角色</button>
                            <div>
                                <label for="char-name-1">姓名:</label>
                                <input type="text" id="char-name-1" name="char-name-1" class="form-input">
                            </div>
                            <div>
                                <label for="char-age-1">年齡:</label>
                                <input type="text" id="char-age-1" name="char-age-1" class="form-input">
                            </div>
                            <div>
                                <label for="char-race-job-1">種族/職業:</label>
                                <input type="text" id="char-race-job-1" name="char-race-job-1" class="form-input">
                            </div>
                            <div>
                                <label for="char-appearance-1">外貌關鍵詞:</label>
                                <input type="text" id="char-appearance-1" name="char-appearance-1" class="form-input" placeholder="例如：銀髮, 機械義眼, 疤痕">
                            </div>
                            <div>
                                <label for="char-goal-1">目標/欲望:</label>
                                <textarea id="char-goal-1" name="char-goal-1" rows="2" class="form-textarea"></textarea>
                            </div>
                            <div>
                                <label for="char-fear-1">恐懼/弱點:</label>
                                <textarea id="char-fear-1" name="char-fear-1" rows="2" class="form-textarea"></textarea>
                            </div>
                            <div>
                                <label for="char-belief-1">信念/口頭禪:</label>
                                <textarea id="char-belief-1" name="char-belief-1" rows="2" class="form-textarea"></textarea>
                            </div>
                            <div>
                                <label for="char-morality-1">道德座標 (九宮格定位):</label>
                                <input type="text" id="char-morality-1" name="char-morality-1" class="form-input" placeholder="例如：守序善良, 混亂中立">
                            </div>
                            <div>
                                <label for="char-relations-1">關鍵關係 (下拉選單選其他角色):</label>
                                <input type="text" id="char-relations-1" name="char-relations-1" class="form-input" placeholder="手動輸入，例如：與角色2 - 盟友">
                            </div>
                            <div>
                                <label for="char-arc-1">角色弧光 (初始狀態 → 轉折點 → 最終成長):</label>
                                <textarea id="char-arc-1" name="char-arc-1" rows="3" class="form-textarea"></textarea>
                            </div>
                            </div>
                        </div>
                        <!-- 更多角色卡片可以動態添加 -->
                    </fieldset>
                    <fieldset>
                        <legend>關係圖譜</legend>
                        <div>
                            <label for="char-relationship-map">角色關係描述:</label>
                            <textarea id="char-relationship-map" name="char-relationship-map" rows="4" class="form-textarea" placeholder="描述角色之間的關係，例如：角色A 與 角色B：仇恨，強度高；角色A 與 角色C：合作，強度中等"></textarea>
                            <p class="form-hint">AI建議：基於角色設定，生成潛在衝突或盟友關係 (此功能將在後端實現)</p>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div id="plot-engine" class="tab-content">
                <h2>情節引擎 ⚙️</h2>
                <form id="plot-engine-form">
                    <fieldset>
                        <legend>核心概念</legend>
                        <div>
                            <label for="plot-logline">一句話故事梗概 (Logline):</label>
                            <input type="text" id="plot-logline" name="plot-logline" class="form-input">
                        </div>
                        <div>
                            <label for="plot-theme">主題思想:</label>
                            <input type="text" id="plot-theme" name="plot-theme" class="form-input">
                        </div>
                        <div>
                            <label for="plot-conflict">核心衝突:</label>
                            <input type="text" id="plot-conflict" name="plot-conflict" class="form-input">
                        </div>
                    </fieldset>
                    <fieldset>
                        <legend>故事結構</legend>
                        <div>
                            <label for="plot-structure-template">結構模板 (可選經典結構):</label>
                            <select id="plot-structure-template" name="plot-structure-template" class="form-select">
                                <option value="three-act">三幕劇</option>
                                <option value="heros-journey">英雄旅程</option>
                                <option value="custom">自定義</option>
                            </select>
                        </div>
                        <div>
                            <label for="plot-beats">關鍵情節點 (Beat Sheet):</label>
                            <textarea id="plot-beats" name="plot-beats" rows="5" class="form-textarea" placeholder="例如：開端、激勵事件、第一幕轉折點..."></textarea>
                            <p class="form-hint">AI建議：根據所選模板和核心概念，生成情節點建議 (此功能將在後端實現)</p>
                        </div>
                    </fieldset>
                    <fieldset>
                        <legend>情節發展</legend>
                        <div id="scene-list">
                            <!-- Scene cards will be added here by JavaScript -->
                        </div>
                        <div>
                            <label for="plot-twists">轉折與驚奇:</label>
                            <textarea id="plot-twists" name="plot-twists" rows="3" class="form-textarea" placeholder="列出主要的劇情轉折點"></textarea>
                        </div>
                        <div>
                            <label for="plot-subplots">次要情節線:</label>
                            <textarea id="plot-subplots" name="plot-subplots" rows="3" class="form-textarea"></textarea>
                        </div>
                        <div>
                            <label for="plot-foreshadowing">伏筆與呼應:</label>
                            <textarea id="plot-foreshadowing" name="plot-foreshadowing" rows="3" class="form-textarea"></textarea>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div id="style-palette" class="tab-content">
                <h2>風格調色盤 🎨</h2>
                <form id="style-palette-form">
                    <fieldset>
                        <legend>文風設定</legend>
                        <div>
                            <label for="style-tone">整體基調:</label>
                            <input type="text" id="style-tone" name="style-tone" class="form-input" placeholder="例如：黑暗、幽默、史詩感">
                        </div>
                        <div>
                            <label for="style-voice">敘事視角與語氣:</label>
                            <input type="text" id="style-voice" name="style-voice" class="form-input" placeholder="例如：第一人稱，不可靠敘事者；第三人稱全知，冷峻客觀">
                        </div>
                        <div>
                            <label for="style-pace">敘事節奏:</label>
                            <select id="style-pace" name="style-pace" class="form-select">
                                <option value="fast">快節奏</option>
                                <option value="medium">中等節奏</option>
                                <option value="slow">慢節奏</option>
                            </select>
                        </div>
                        <div>
                            <label for="style-vocabulary">詞彙風格:</label>
                            <input type="text" id="style-vocabulary" name="style-vocabulary" class="form-input" placeholder="例如：簡潔、華麗、學術、口語化">
                        </div>
                        <div>
                            <label for="style-sentence-structure">句式偏好:</label>
                            <input type="text" id="style-sentence-structure" name="style-sentence-structure" class="form-input" placeholder="例如：多短句、長短句結合、多從句">
                        </div>
                    </fieldset>
                    <fieldset>
                        <legend>修辭與感官</legend>
                        <div>
                            <label for="style-rhetoric">常用修辭手法:</label>
                            <input type="text" id="style-rhetoric" name="style-rhetoric" class="form-input" placeholder="例如：隱喻、明喻、排比、反諷">
                        </div>
                        <div>
                            <label for="style-sensory-details">感官描寫偏重:</label>
                            <input type="text" id="style-sensory-details" name="style-sensory-details" class="form-input" placeholder="例如：視覺、聽覺、嗅覺、觸覺、味覺">
                        </div>
                    </fieldset>
                    <fieldset>
                        <legend>參考風格</legend>
                        <div>
                            <label for="style-reference-authors">參考作家/作品:</label>
                            <textarea id="style-reference-authors" name="style-reference-authors" rows="3" class="form-textarea" placeholder="列出風格相似的作家或作品，AI可學習其特點"></textarea>
                        </div>
                    </fieldset>
                </form>
            </div>
            <div id="scene-library" class="tab-content">
                <h2>場景庫 📚</h2>
                <form id="scene-library-form">
                    <fieldset>
                        <legend>場景管理</legend>
                        <button type="button" id="add-scene-btn">新增場景</button>
                        <div id="scene-list">
                            <!-- 場景卡片將動態添加到這裡 -->
                            <div class="scene-card">
                                <h3>場景1: [場景標題]</h3>
                                <div>
                                    <label for="scene-title-1">場景標題:</label>
                                    <input type="text" id="scene-title-1" name="scene-title-1" class="form-input">
                                </div>
                                <div>
                                    <label for="scene-summary-1">場景摘要:</label>
                                    <textarea id="scene-summary-1" name="scene-summary-1" rows="2" class="form-textarea"></textarea>
                                </div>
                                <div>
                                    <label for="scene-characters-1">出場角色:</label>
                                    <input type="text" id="scene-characters-1" name="scene-characters-1" class="form-input" placeholder="選擇或輸入角色">
                                </div>
                                <div>
                                    <label for="scene-setting-1">地點/時間:</label>
                                    <input type="text" id="scene-setting-1" name="scene-setting-1" class="form-input">
                                </div>
                                <div>
                                    <label for="scene-plot-points-1">關鍵情節點推進:</label>
                                    <textarea id="scene-plot-points-1" name="scene-plot-points-1" rows="2" class="form-textarea"></textarea>
                                </div>
                                <div>
                                    <label for="scene-content-1">場景初稿 (AI輔助生成/手動輸入):</label>
                                    <textarea id="scene-content-1" name="scene-content-1" rows="5" class="form-textarea"></textarea>
                                </div>
                                <button type="button" class="remove-scene-btn">移除此場景</button>
                            </div>
                        </div>
                    </fieldset>
                    <fieldset>
                        <legend>場景排序與篩選</legend>
                        <div>
                            <label for="scene-sort-by">排序方式:</label>
                            <select id="scene-sort-by" name="scene-sort-by" class="form-select">
                                <option value="chronological">時間順序</option>
                                <option value="importance">重要性</option>
                                <option value="character">按角色</option>
                            </select>
                        </div>
                        <div>
                            <label for="scene-filter">篩選條件:</label>
                            <input type="text" id="scene-filter" name="scene-filter" class="form-input" placeholder="例如：包含角色A的場景">
                        </div>
                    </fieldset>
                </form>
            </div>
            <div id="rulebook" class="tab-content">
                <h2>規則手冊 📜</h2>
                <form id="rulebook-form">
                    <fieldset>
                        <legend>寫作規則與風格指南</legend>
                        <div>
                            <label for="rule-writing-style">整體寫作風格偏好:</label>
                            <textarea id="rule-writing-style" name="rule-writing-style" rows="3" class="form-textarea" placeholder="例如：避免使用被動語態，多用感官描寫，保持快節奏..."></textarea>
                        </div>
                        <div>
                            <label for="rule-dos">應該做 (Do's):</label>
                            <textarea id="rule-dos" name="rule-dos" rows="3" class="form-textarea" placeholder="列出寫作時應遵循的正面規則"></textarea>
                        </div>
                        <div>
                            <label for="rule-donts">不應該做 (Don'ts):</label>
                            <textarea id="rule-donts" name="rule-donts" rows="3" class="form-textarea" placeholder="列出寫作時應避免的負面規則"></textarea>
                        </div>
                        <div>
                            <label for="rule-custom-glossary">自定義詞彙表/術語:</label>
                            <textarea id="rule-custom-glossary" name="rule-custom-glossary" rows="3" class="form-textarea" placeholder="每行一個詞彙及其解釋，例如：光能核心 - 提供城市能源的神秘裝置"></textarea>
                        </div>
                    </fieldset>
                    <fieldset>
                        <legend>AI協作偏好</legend>
                        <div>
                            <label for="rule-ai-suggestion-level">AI建議程度:</label>
                            <select id="rule-ai-suggestion-level" name="rule-ai-suggestion-level" class="form-select">
                                <option value="minimal">最少干預 (僅修正)</option>
                                <option value="moderate">適度建議 (提供選項)</option>
                                <option value="proactive">主動擴寫 (大膽創作)</option>
                            </select>
                        </div>
                        <div>
                            <label for="rule-ai-creativity-level">AI創意發揮程度:</label>
                            <input type="range" id="rule-ai-creativity-level" name="rule-ai-creativity-level" min="0" max="100" value="50" class="form-range">
                            <span id="ai-creativity-value">50</span>%
                        </div>
                        <div>
                            <label for="rule-ai-focus-areas">AI輔助重點:</label>
                            <input type="text" id="rule-ai-focus-areas" name="rule-ai-focus-areas" class="form-input" placeholder="例如：情節發展、角色對話、環境描寫">
                        </div>
                    </fieldset>
                </form>
            </div>

            <!-- AI設定頁籤 -->
            <div id="ai-settings" class="tab-content">
                <h2>AI設定 🤖</h2>

                <!-- 當前AI服務狀態 -->
                <div class="ai-status-panel">
                    <h3>當前AI服務狀態</h3>
                    <div id="current-ai-status">
                        <p>載入中...</p>
                    </div>
                </div>

                <!-- AI服務配置 -->
                <form id="ai-config-form">
                    <fieldset>
                        <legend>AI服務選擇</legend>
                        <div id="ai-services-list">
                            <!-- 動態載入的AI服務列表 -->
                        </div>
                    </fieldset>

                    <fieldset>
                        <legend>API金鑰設定</legend>
                        <div class="form-group">
                            <label for="ai-service-select">選擇服務:</label>
                            <select id="ai-service-select" class="form-select">
                                <option value="">請選擇AI服務</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="api-key-input">API金鑰:</label>
                            <input type="password" id="api-key-input" class="form-input" placeholder="請輸入API金鑰">
                            <button type="button" id="toggle-api-key-visibility" class="btn btn-secondary btn-sm">顯示</button>
                        </div>
                        <div class="form-group">
                            <button type="button" id="save-api-key" class="btn btn-primary">儲存API金鑰</button>
                            <button type="button" id="test-ai-connection" class="btn btn-secondary">測試連接</button>
                        </div>
                        <div id="api-key-status" class="form-hint">
                            <!-- API金鑰狀態顯示 -->
                        </div>
                    </fieldset>

                    <fieldset>
                        <legend>生成參數設定</legend>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="ai-temperature">創意程度 (Temperature):</label>
                                <input type="range" id="ai-temperature" class="form-range" min="0" max="1" step="0.1" value="0.7">
                                <span id="temperature-value">0.7</span>
                            </div>
                            <div class="form-group">
                                <label for="ai-max-tokens">最大輸出長度:</label>
                                <input type="number" id="ai-max-tokens" class="form-input" min="100" max="4000" value="2048">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="ai-top-p">多樣性 (Top-p):</label>
                            <input type="range" id="ai-top-p" class="form-range" min="0" max="1" step="0.1" value="0.9">
                            <span id="top-p-value">0.9</span>
                        </div>
                        <div class="form-group">
                            <button type="button" id="save-generation-settings" class="btn btn-primary">儲存生成設定</button>
                            <button type="button" id="reset-generation-settings" class="btn btn-secondary">重置為預設值</button>
                        </div>
                    </fieldset>
                </form>

                <!-- AI服務說明 -->
                <fieldset>
                    <legend>AI服務說明</legend>
                    <div id="ai-service-info">
                        <div class="service-info">
                            <h4>Google Gemini</h4>
                            <p>Google的先進AI模型，支援多語言對話和創作。適合小說創作、角色對話生成等任務。</p>
                            <p><strong>獲取API金鑰：</strong> <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></p>
                        </div>
                        <div class="service-info">
                            <h4>OpenAI GPT (即將支援)</h4>
                            <p>OpenAI的GPT模型，強大的文本生成能力，適合各種創作任務。</p>
                            <p><strong>獲取API金鑰：</strong> <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a></p>
                        </div>
                        <div class="service-info">
                            <h4>Anthropic Claude (即將支援)</h4>
                            <p>Anthropic的Claude模型，擅長長文本理解和創作，適合複雜的故事情節構建。</p>
                            <p><strong>獲取API金鑰：</strong> <a href="https://console.anthropic.com/" target="_blank">Anthropic Console</a></p>
                        </div>
                    </div>
                </fieldset>
            </div>
        </main>

        <!-- HTML Templates for Cards -->
        <template id="character-card-template">
            <div class="character-card">
                <h3>角色<span class="character-count"></span></h3> <button type="button" class="add-character-btn">新增角色</button> <button type="button" class="remove-character-btn">移除此角色</button>
                <div>
                    <label for="char-name">姓名:</label>
                    <input type="text" name="char-name" class="form-input char-name">
                </div>
                <div>
                    <label for="char-age">年齡:</label>
                    <input type="text" name="char-age" class="form-input char-age">
                </div>
                <div>
                    <label for="char-race-job">種族/職業:</label>
                    <input type="text" name="char-race-job" class="form-input char-race-job">
                </div>
                <div>
                    <label for="char-appearance">外貌關鍵詞:</label>
                    <input type="text" name="char-appearance" class="form-input char-appearance" placeholder="例如：銀髮, 機械義眼, 疤痕">
                </div>
                <div>
                    <label for="char-goal">目標/欲望:</label>
                    <textarea name="char-goal" rows="2" class="form-textarea char-goal"></textarea>
                </div>
                <div>
                    <label for="char-fear">恐懼/弱點:</label>
                    <textarea name="char-fear" rows="2" class="form-textarea char-fear"></textarea>
                </div>
                <div>
                    <label for="char-belief">信念/口頭禪:</label>
                    <textarea name="char-belief" rows="2" class="form-textarea char-belief"></textarea>
                </div>
                <div>
                    <label for="char-morality">道德座標 (九宮格定位):</label>
                    <input type="text" name="char-morality" class="form-input char-morality" placeholder="例如：守序善良, 混亂中立">
                </div>
                <div>
                    <label for="char-relations">關鍵關係:</label>
                    <input type="text" name="char-relations" class="form-input char-relations" placeholder="手動輸入，例如：與角色2 - 盟友">
                </div>
                <div>
                    <label for="char-arc">角色弧光 (初始狀態 → 轉折點 → 最終成長):</label>
                    <textarea name="char-arc" rows="3" class="form-textarea char-arc"></textarea>
                </div>
            </div>
        </template>

        <template id="scene-card-template">
            <div class="scene-card">
                <h4>場景<span class="scene-count"></span></h4> <button type="button" class="add-scene-btn">新增場景</button> <button type="button" class="remove-scene-btn">移除此場景</button>
                <div>
                    <label for="scene-title">場景標題/簡述:</label>
                    <input type="text" name="scene-title" class="form-input scene-title">
                </div>
                <div>
                    <label for="scene-location">地點:</label>
                    <input type="text" name="scene-location" class="form-input scene-location">
                </div>
                <div>
                    <label for="scene-time">時間:</label>
                    <input type="text" name="scene-time" class="form-input scene-time">
                </div>
                <div>
                    <label for="scene-characters">參與角色:</label>
                    <input type="text" name="scene-characters" class="form-input scene-characters" placeholder="以逗號分隔">
                </div>
                <div>
                    <label for="scene-event">核心事件:</label>
                    <textarea name="scene-event" rows="2" class="form-textarea scene-event"></textarea>
                </div>
                <div>
                    <label for="scene-atmosphere">氛圍/情緒:</label>
                    <input type="text" name="scene-atmosphere" class="form-input scene-atmosphere">
                </div>
                 <div>
                    <label for="scene-dialogue-summary">對白概要:</label>
                    <textarea name="scene-dialogue-summary" rows="2" class="form-textarea scene-dialogue-summary"></textarea>
                </div>
                <div>
                    <label for="scene-plot-advancement">推動情節:</label>
                    <textarea name="scene-plot-advancement" rows="2" class="form-textarea scene-plot-advancement"></textarea>
                </div>
            </div>
        </template>

        <!-- 右側：AI即時預覽區 & 設定回填區 -->
        <aside class="ai-preview-panel">
            <h3>AI 生成沙盒</h3>
            <textarea id="ai-sandbox-output" rows="10" placeholder="AI 生成的草稿/示例/擴展將顯示於此..."></textarea>
            <button id="send-to-sandbox">傳送至AI沙盒生成</button>
            <button id="fill-back-settings">回填至設定</button>
            <hr>
            <h3>設定完整性檢查</h3>
            <div id="completeness-check-results">
                <!-- 檢查結果將顯示於此 -->
            </div>
        </aside>
    </div>

    <!-- 底部：全局控制欄 -->
    <footer class="global-controls">
        <button id="integrate-generate">整合生成小說初稿</button>
        <button id="check-completeness">檢查設定完整性</button>
    </footer>

    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <!-- Toast Notification -->
    <div id="toast-notification" class="toast" style="position: fixed; top: 20px; right: 20px; z-index: 1050; display: none;">
        <div class="toast-header">
            <strong class="me-auto">通知</strong>
            <button type="button" class="btn-close" onclick="document.getElementById('toast-notification').style.display='none'"></button>
        </div>
        <div class="toast-body">
            <!-- Toast message will appear here -->
        </div>
    </div>
</body>
</html>